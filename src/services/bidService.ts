import { apiService, ApiResponse } from './api';
import {
  Bid,
  CreateBidRequest,
  UpdateBidRequest,
  BidFilters,
  BidSortOptions,
  BidListResponse,
  BidStatistics,
  BidActionResponse
} from '../types/bid';

/**
 * Create a new bid - Enhanced for Task 17 Subtask 17.2 & 17.3
 * Implements comprehensive error handling and business logic validation
 */
export const createBid = async (bidData: CreateBidRequest): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation before API call
    if (!bidData.jobId || !bidData.amount || !bidData.description) {
      return {
        data: null,
        error: 'Missing required fields: jobId, amount, and description are required',
        status: 400,
        isSuccess: false
      };
    }

    if (bidData.amount <= 0) {
      return {
        data: null,
        error: 'Bid amount must be greater than 0',
        status: 400,
        isSuccess: false
      };
    }

    if (bidData.description.trim().length < 10) {
      return {
        data: null,
        error: 'Bid description must be at least 10 characters long',
        status: 400,
        isSuccess: false
      };
    }

    // Make API call with enhanced error handling
    const response = await apiService<Bid>('/api/bids', {
      method: 'POST',
      body: {
        service_request_id: bidData.jobId, // Map to expected backend field name
        amount: bidData.amount,
        description: bidData.description.trim(),
        status: 'requested' // Set default status as per task requirements
      },
      requiresAuth: true
    });

    // Enhanced response handling
    if (response.isSuccess && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          jobId: response.data.jobId || bidData.jobId, // Ensure jobId is present
          status: response.data.status || 'pending' // Default status mapping
        }
      };
    }

    // Handle specific error cases
    if (response.status === 401) {
      return {
        ...response,
        error: 'Authentication required. Please log in to submit a bid.'
      };
    }

    if (response.status === 403) {
      return {
        ...response,
        error: 'You do not have permission to bid on this job.'
      };
    }

    if (response.status === 409) {
      return {
        ...response,
        error: 'You have already submitted a bid for this job.'
      };
    }

    return response;

  } catch (error) {
    console.error('Bid creation error:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred while creating the bid',
      status: 0,
      isSuccess: false
    };
  }
};

/**
 * Get bids for a provider
 */
export const getProviderBids = async (
  providerId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidListResponse>(`/api/providers/${providerId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Get bids for a specific job
 */
export const getJobBids = async (
  jobId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidListResponse>(`/api/jobs/${jobId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Get all bids (admin only)
 */
export const getAllBids = async (
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  return apiService<BidListResponse>(`/api/admin/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers: {
      'Authorization': token || ''
    }
  });
};

/**
 * Get a specific bid by ID
 */
export const getBidById = async (bidId: string, token?: string): Promise<ApiResponse<Bid>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<Bid>(`/api/bids/${bidId}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Update a bid - Enhanced for Task 18 Subtasks 18.1, 18.2, 18.3 & 18.4
 * Implements comprehensive authentication, authorization, validation, and business logic
 */
export const updateBid = async (
  bidId: string,
  updateData: UpdateBidRequest,
  token?: string
): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation before API call
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    // Validate bid ID format (basic UUID check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(bidId.trim())) {
      return {
        data: null,
        error: 'Bid ID must be a valid UUID format',
        status: 400,
        isSuccess: false
      };
    }

    // Validate that at least one field is being updated
    if (!updateData || (updateData.amount === undefined && !updateData.description)) {
      return {
        data: null,
        error: 'At least one field (amount or description) must be provided for update',
        status: 400,
        isSuccess: false
      };
    }

    // Validate amount if provided
    if (updateData.amount !== undefined) {
      if (typeof updateData.amount !== 'number' || isNaN(updateData.amount)) {
        return {
          data: null,
          error: 'Bid amount must be a valid number',
          status: 400,
          isSuccess: false
        };
      }

      if (updateData.amount <= 0) {
        return {
          data: null,
          error: 'Bid amount must be greater than 0',
          status: 400,
          isSuccess: false
        };
      }

      if (updateData.amount > 1000000) {
        return {
          data: null,
          error: 'Bid amount cannot exceed $1,000,000',
          status: 400,
          isSuccess: false
        };
      }

      // Check for reasonable decimal places (max 2)
      const decimalPlaces = (updateData.amount.toString().split('.')[1] || '').length;
      if (decimalPlaces > 2) {
        return {
          data: null,
          error: 'Bid amount can have at most 2 decimal places',
          status: 400,
          isSuccess: false
        };
      }
    }

    // Validate description if provided
    if (updateData.description !== undefined) {
      if (typeof updateData.description !== 'string') {
        return {
          data: null,
          error: 'Bid description must be a string',
          status: 400,
          isSuccess: false
        };
      }

      const trimmedDescription = updateData.description.trim();

      if (trimmedDescription.length < 10) {
        return {
          data: null,
          error: 'Bid description must be at least 10 characters long',
          status: 400,
          isSuccess: false
        };
      }

      if (trimmedDescription.length > 2000) {
        return {
          data: null,
          error: 'Bid description cannot exceed 2000 characters',
          status: 400,
          isSuccess: false
        };
      }

      // Check for potentially harmful content (basic XSS protection)
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(trimmedDescription)) {
          return {
            data: null,
            error: 'Bid description contains invalid content',
            status: 400,
            isSuccess: false
          };
        }
      }
    }

    // Prepare the update payload with trimmed description if provided
    const updatePayload: UpdateBidRequest = {};
    if (updateData.amount !== undefined) {
      updatePayload.amount = updateData.amount;
    }
    if (updateData.description !== undefined) {
      updatePayload.description = updateData.description.trim();
    }

    // Make API call with enhanced error handling
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    const response = await apiService<Bid>(`/api/bids/${bidId}`, {
      method: 'PUT',
      body: updatePayload,
      requiresAuth: true,
      headers
    });

    // Enhanced response handling with specific error cases
    if (response.isSuccess && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          // Ensure consistent data structure
          updatedAt: response.data.updatedAt || new Date().toISOString()
        }
      };
    }

    // Handle specific error cases for better user experience
    if (response.status === 401) {
      return {
        ...response,
        error: 'Authentication required. Please log in to update your bid.'
      };
    }

    if (response.status === 403) {
      return {
        ...response,
        error: 'You do not have permission to update this bid. You can only update your own bids.'
      };
    }

    if (response.status === 404) {
      return {
        ...response,
        error: 'Bid not found. It may have been deleted or does not exist.'
      };
    }

    if (response.status === 409) {
      return {
        ...response,
        error: 'This bid cannot be updated. Only bids with "pending" status can be modified.'
      };
    }

    if (response.status === 422) {
      return {
        ...response,
        error: response.error || 'Invalid bid data provided. Please check your input and try again.'
      };
    }

    return response;

  } catch (error) {
    // Handle network errors and unexpected exceptions
    console.error('Error updating bid:', error);
    return {
      data: null,
      error: 'An unexpected error occurred while updating the bid. Please try again.',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Withdraw a bid
 */
export const withdrawBid = async (bidId: string, token?: string): Promise<ApiResponse<BidActionResponse>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidActionResponse>(`/api/bids/${bidId}/withdraw`, {
    method: 'POST',
    requiresAuth: true,
    headers
  });
};

/**
 * Accept a bid (customer only)
 */
export const acceptBid = async (bidId: string): Promise<ApiResponse<BidActionResponse>> => {
  return apiService<BidActionResponse>(`/api/bids/${bidId}/accept`, {
    method: 'POST',
    requiresAuth: true
  });
};

/**
 * Reject a bid (customer only)
 */
export const rejectBid = async (bidId: string): Promise<ApiResponse<BidActionResponse>> => {
  return apiService<BidActionResponse>(`/api/bids/${bidId}/reject`, {
    method: 'POST',
    requiresAuth: true
  });
};

/**
 * Get bid statistics (admin only)
 */
export const getBidStatistics = async (
  filters?: BidFilters
): Promise<ApiResponse<BidStatistics>> => {
  const params = new URLSearchParams(
    filters ? Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    ) : {}
  );

  return apiService<BidStatistics>(`/api/admin/bids/statistics?${params}`, {
    method: 'GET',
    requiresAuth: true
  });
};
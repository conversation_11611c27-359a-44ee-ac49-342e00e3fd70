import React from 'react';
import { Route, Routes, Navigate, Outlet } from 'react-router-dom';
import RequireAuth from '@auth-kit/react-router/RequireAuth';
import { useAuth } from '@/features/auth/hooks/useAuth';

// Import provider pages
import ProviderDashboard from '@/pages/ProviderDashboard';
import ProviderJobs from '@/pages/ProviderJobs';
import ProviderLeads from '@/pages/ProviderLeads';
import ProviderProfile from '@/pages/ProviderProfile';
import ProviderMessages from '@/pages/ProviderMessages';
import ProviderPlans from '@/pages/ProviderPlans';

// Import the provider layout
import ProviderLayout from '@/components/provider/ProviderLayout';

/**
 * Provider Routes Component
 *
 * This component handles all routes that should only be accessible to providers.
 * It uses RequireAuth from @auth-kit/react-router to protect routes and redirects
 * non-providers to the appropriate page.
 */
const ProviderRoutes: React.FC = () => {
  const { isProvider, rolesLoading } = useAuth();

  // Provider check component
  const ProviderCheck = () => {
    // If roles are still loading, don't redirect yet
    if (rolesLoading) {
      // You could return a loading spinner here if desired
      return <div></div>;
    }

    // Once roles are loaded, check if user is a provider
    return isProvider ? <Outlet /> : <Navigate to="/" replace />;
  };

  return (
    <Routes>
      <Route element={
        <RequireAuth fallbackPath="/auth">
          <ProviderCheck />
        </RequireAuth>
      }>
        {/* Apply the ProviderLayout to all provider routes */}
        <Route element={<ProviderLayout />}>
          {/* Provider routes */}
          <Route path="/dashboard" element={<ProviderDashboard />} />
          <Route path="/jobs" element={<ProviderJobs />} />
          <Route path="/leads" element={<ProviderLeads />} />
          <Route path="/profile" element={<ProviderProfile />} />
          <Route path="/messages" element={<ProviderMessages />} />
          <Route path="/plans" element={<ProviderPlans />} />

          {/* Catch-all route for provider section */}
          <Route path="*" element={<Navigate to="/provider/dashboard" replace />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default ProviderRoutes;

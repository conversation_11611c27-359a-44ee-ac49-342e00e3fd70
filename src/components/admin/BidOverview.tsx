import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getAllBids, getBidStatistics } from '@/services/bidService';
import { Bid, BidFilters, BidSortOptions, BidStatistics } from '@/types/bid';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Minus,
  Loader2,
  Eye,
  BarChart3
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface BidOverviewProps {
  onBidSelect?: (bid: Bid) => void;
}

export const BidOverview: React.FC<BidOverviewProps> = ({ onBidSelect }) => {
  const [bids, setBids] = useState<Bid[]>([]);
  const [statistics, setStatistics] = useState<BidStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [filters, setFilters] = useState<BidFilters>({});
  const [sort, setSort] = useState<BidSortOptions>({ field: 'submittedAt', direction: 'desc' });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const { token } = useAuth();

  const fetchBids = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getAllBids(filters, sort, page, 20 , token || undefined);
      
      if (response.isSuccess && response.data) {
        setBids(response.data.bids);
        setTotalPages(response.data.totalPages);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch bids',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [filters, sort, page, toast]);

  const fetchStatistics = useCallback(async () => {
    setStatsLoading(true);
    try {
      const response = await getBidStatistics(filters);
      
      if (response.isSuccess && response.data) {
        setStatistics(response.data);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch statistics',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setStatsLoading(false);
    }
  }, [filters, toast]);

  useEffect(() => {
    fetchBids();
    fetchStatistics();
  }, [fetchBids, fetchStatistics]);

  const handleFilterChange = (key: keyof BidFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' || !value ? undefined : value
    }));
    setPage(1);
  };

  const handleSortChange = (field: string) => {
    setSort(prev => ({
      field: field as BidSortOptions['field'],
      direction: prev.field === field && prev.direction === 'desc' ? 'asc' : 'desc'
    }));
    setPage(1);
  };

  const getBidStatusColor = (status: string): string => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const pieChartData = statistics ? [
    { name: 'Pending', value: statistics.bidsByStatus.pending, color: '#fbbf24' },
    { name: 'Accepted', value: statistics.bidsByStatus.accepted, color: '#10b981' },
    { name: 'Rejected', value: statistics.bidsByStatus.rejected, color: '#ef4444' },
    { name: 'Withdrawn', value: statistics.bidsByStatus.withdrawn, color: '#6b7280' }
  ] : [];

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    trend?: 'up' | 'down' | 'neutral';
    trendValue?: string;
  }> = ({ title, value, icon, trend, trendValue }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {trend && trendValue && (
              <div className={`flex items-center mt-1 text-sm ${
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {trend === 'up' && <TrendingUp className="h-4 w-4 mr-1" />}
                {trend === 'down' && <TrendingDown className="h-4 w-4 mr-1" />}
                {trend === 'neutral' && <Minus className="h-4 w-4 mr-1" />}
                {trendValue}
              </div>
            )}
          </div>
          <div className="text-gray-400">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (statsLoading && loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Bids"
            value={statistics.totalBids.toLocaleString()}
            icon={<FileText className="h-8 w-8" />}
          />
          <StatCard
            title="Pending Bids"
            value={statistics.pendingBids.toLocaleString()}
            icon={<Clock className="h-8 w-8" />}
          />
          <StatCard
            title="Accepted Bids"
            value={statistics.acceptedBids.toLocaleString()}
            icon={<CheckCircle className="h-8 w-8" />}
          />
          <StatCard
            title="Total Bid Value"
            value={formatCurrency(statistics.totalBidValue)}
            icon={<DollarSign className="h-8 w-8" />}
          />
        </div>
      )}

      {/* Charts */}
      {statistics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bid Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Bid Status Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Monthly Bid Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Bid Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={statistics.bidsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="count" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    name="Bid Count"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Bids</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search by job or provider..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <Select onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="withdrawn">Withdrawn</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Input
                type="number"
                placeholder="Min amount"
                onChange={(e) => handleFilterChange('minAmount', e.target.value)}
              />
            </div>
            <div>
              <Input
                type="number"
                placeholder="Max amount"
                onChange={(e) => handleFilterChange('maxAmount', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bid List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>All Bids ({bids.length})</span>
            <div className="flex gap-2">
              <Button
                variant={sort.field === 'submittedAt' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleSortChange('submittedAt')}
              >
                Date {sort.field === 'submittedAt' && (sort.direction === 'desc' ? '↓' : '↑')}
              </Button>
              <Button
                variant={sort.field === 'amount' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleSortChange('amount')}
              >
                Amount {sort.field === 'amount' && (sort.direction === 'desc' ? '↓' : '↑')}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : bids.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No bids found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {bids.map((bid) => (
                <div key={bid.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold">
                        {bid.job?.title || 'Job Title'}
                      </h3>
                      <p className="text-sm text-gray-600 mb-1">
                        Provider: {bid.provider?.firstName} {bid.provider?.lastName}
                        {bid.provider?.businessName && ` (${bid.provider.businessName})`}
                      </p>
                      <p className="text-sm text-gray-600">
                        Customer: {bid.customer?.firstName} {bid.customer?.lastName}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-green-600 mb-1">
                        {formatCurrency(bid.amount)}
                      </div>
                      <Badge className={getBidStatusColor(bid.status)}>
                        {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                    {bid.description}
                  </p>
                  
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>Submitted: {formatDate(bid.submittedAt)}</span>
                    {onBidSelect && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onBidSelect(bid)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};